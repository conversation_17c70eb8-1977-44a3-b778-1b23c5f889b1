"use client";

import { useEffect, useState } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { getAllReviews, updateReview, deleteReview, getUserProfile } from "@/lib/firebase/firestore";
import { Review, UserProfile } from "@/types/models";
import { formatDate } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";

// Enhanced review type with user profile data
interface ReviewWithUserData extends Review {
  userProfile?: UserProfile;
  displayName?: string;
}

export default function AdminReviewsPage() {
  const { t, isClient } = useLocale();
  const [reviews, setReviews] = useState<ReviewWithUserData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<"all" | "approved" | "pending">("all");
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Fetch all reviews with user profile data
  useEffect(() => {
    const fetchReviews = async () => {
      try {
        setIsLoading(true);
        const allReviews = await getAllReviews();

        // Fetch user profiles for each review
        const reviewsWithUserData = await Promise.all(
          allReviews.map(async (review) => {
            try {
              const userProfile = await getUserProfile(review.userId);
              const displayName = userProfile?.displayName ||
                                userProfile?.email?.split('@')[0] ||
                                review.customerName ||
                                'Customer';

              return {
                ...review,
                userProfile,
                displayName
              } as ReviewWithUserData;
            } catch (error) {
              console.error(`Error fetching user profile for ${review.userId}:`, error);
              // Fallback to stored customer name or default
              return {
                ...review,
                displayName: review.customerName || 'Customer'
              } as ReviewWithUserData;
            }
          })
        );

        setReviews(reviewsWithUserData);
      } catch (error) {
        console.error("Error fetching reviews:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchReviews();
  }, []);

  // Handle approve review
  const handleApproveReview = async (reviewId: string) => {
    setActionLoading(reviewId);
    try {
      await updateReview(reviewId, { isApproved: true, updatedAt: new Date() });

      // Update local state
      setReviews(prev => prev.map(review =>
        review.id === reviewId
          ? { ...review, isApproved: true, updatedAt: new Date() } as ReviewWithUserData
          : review
      ));

      toast({
        title: t('admin.reviewApproved'),
        description: t('admin.reviewApprovedDescription'),
        variant: 'success',
      });
    } catch (error) {
      console.error('Error approving review:', error);
      toast({
        title: t('admin.error'),
        description: t('admin.reviewApproveError'),
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  // Handle delete review
  const handleDeleteReview = async (reviewId: string) => {
    if (!confirm(t('admin.confirmDeleteReview'))) return;

    setActionLoading(reviewId);
    try {
      await deleteReview(reviewId);

      // Update local state
      setReviews(prev => prev.filter(review => review.id !== reviewId));

      toast({
        title: t('admin.reviewDeleted'),
        description: t('admin.reviewDeletedDescription'),
        variant: 'success',
      });
    } catch (error) {
      console.error('Error deleting review:', error);
      toast({
        title: t('admin.error'),
        description: t('admin.reviewDeleteError'),
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  // Filter reviews based on search and status
  const filteredReviews = reviews.filter(review => {
    const matchesSearch =
      review.displayName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.comment?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.orderNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.userProfile?.email?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      filterStatus === "all" ||
      (filterStatus === "approved" && review.isApproved) ||
      (filterStatus === "pending" && !review.isApproved);

    return matchesSearch && matchesStatus;
  });

  // Star Rating Display Component
  const StarRating = ({ rating }: { rating: number }) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map(star => (
          <i
            key={star}
            className={`fa-${star <= rating ? 'solid' : 'regular'} fa-star ${
              star <= rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'
            } text-sm`}
          />
        ))}
      </div>
    );
  };

  // Calculate statistics
  const totalReviews = reviews.length;
  const approvedReviews = reviews.filter(r => r.isApproved).length;
  const pendingReviews = reviews.filter(r => !r.isApproved).length;
  const averageRating = reviews.length > 0
    ? (reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length).toFixed(1)
    : "0.0";

  if (!isClient) return null;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          {t('admin.reviewsSection')}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {t('admin.reviewsDescription')}
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('admin.totalReviews')}</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">{totalReviews}</p>
            </div>
            <i className="fa-solid fa-star text-2xl text-yellow-500"></i>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('admin.approvedReviews')}</p>
              <p className="text-2xl font-bold text-green-600">{approvedReviews}</p>
            </div>
            <i className="fa-solid fa-check-circle text-2xl text-green-500"></i>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('admin.pendingReviews')}</p>
              <p className="text-2xl font-bold text-orange-600">{pendingReviews}</p>
            </div>
            <i className="fa-solid fa-clock text-2xl text-orange-500"></i>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">{t('admin.averageRating')}</p>
              <p className="text-2xl font-bold text-blue-600">{averageRating}</p>
            </div>
            <i className="fa-solid fa-chart-line text-2xl text-blue-500"></i>
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <input
              type="text"
              placeholder={t('admin.searchReviews')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant={filterStatus === "all" ? "default" : "outline"}
              onClick={() => setFilterStatus("all")}
              className="bg-[#56999B] dark:bg-[#5DBDC0] hover:bg-[#56999B]/90 dark:hover:bg-[#4A9EA0]"
            >
              {t('admin.allReviews')}
            </Button>
            <Button
              variant={filterStatus === "approved" ? "default" : "outline"}
              onClick={() => setFilterStatus("approved")}
              className="bg-green-600 hover:bg-green-700"
            >
              {t('admin.approved')}
            </Button>
            <Button
              variant={filterStatus === "pending" ? "default" : "outline"}
              onClick={() => setFilterStatus("pending")}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {t('admin.pending')}
            </Button>
          </div>
        </div>

        {/* Reviews List */}
        {isLoading ? (
          <div className="flex justify-center p-16">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : filteredReviews.length > 0 ? (
          <div className="space-y-4">
            {filteredReviews.map(review => (
              <div key={review.id} className="border dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-[#242832] transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <StarRating rating={review.rating} />
                      <Badge variant={review.isApproved ? "default" : "secondary"}>
                        {review.isApproved ? t('admin.approved') : t('admin.pending')}
                      </Badge>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {review.orderNumber}
                      </span>
                    </div>

                    <div className="mb-2">
                      <p className="font-medium text-gray-800 dark:text-gray-200">
                        {review.displayName || t('admin.anonymousCustomer')}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {review.userProfile?.email || review.customerEmail}
                      </p>
                    </div>

                    {review.comment && (
                      <p className="text-gray-600 dark:text-gray-400 mb-3">
                        "{review.comment}"
                      </p>
                    )}

                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {review.createdAt instanceof Date
                        ? formatDate(review.createdAt)
                        : typeof review.createdAt === 'string'
                          ? formatDate(new Date(review.createdAt))
                          : ''}
                    </div>
                  </div>

                  <div className="flex gap-2 ml-4">
                    {!review.isApproved && (
                      <Button
                        size="sm"
                        className="bg-green-600 hover:bg-green-700 text-white"
                        onClick={() => handleApproveReview(review.id)}
                        disabled={actionLoading === review.id}
                      >
                        {actionLoading === review.id ? (
                          <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-1" />
                        ) : (
                          <i className="fa-solid fa-check mr-1"></i>
                        )}
                        {t('admin.approve')}
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-red-600 border-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                      onClick={() => handleDeleteReview(review.id)}
                      disabled={actionLoading === review.id}
                    >
                      {actionLoading === review.id ? (
                        <div className="animate-spin w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full mr-1" />
                      ) : (
                        <i className="fa-solid fa-trash mr-1"></i>
                      )}
                      {t('admin.delete')}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <i className="fa-solid fa-star text-5xl text-gray-300 dark:text-gray-600 mb-4"></i>
            <h3 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-2">
              {t('admin.noReviewsFound')}
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm || filterStatus !== "all"
                ? t('admin.noReviewsMatchFilter')
                : t('admin.noReviewsYet')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}