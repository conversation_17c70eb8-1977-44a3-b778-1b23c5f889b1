"use client";

import { useState, useRef } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import { createReview } from "@/lib/firebase/firestore";
import { Order } from "@/types/models";
import { Dialog, DialogContent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface OrderReviewPromptProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onReviewSubmitted?: () => void;
}

export default function OrderReviewPrompt({ 
  order, 
  isOpen, 
  onClose, 
  onReviewSubmitted 
}: OrderReviewPromptProps) {
  const { t, isClient } = useLocale();
  const { user } = useAuth();
  const [rating, setRating] = useState(5);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const commentRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!order || !user) return;

    setIsSubmitting(true);
    try {
      await createReview({
        userId: user.uid,
        orderId: order.id,
        orderNumber: `#${order.id.slice(-8).toUpperCase()}`,
        customerName: user.displayName || user.email || 'Anonymous',
        customerEmail: user.email || '',
        rating,
        comment: commentRef.current?.value || '',
        isApproved: true, // Auto-approve reviews for now
        createdAt: new Date(),
        updatedAt: new Date()
      });

      onReviewSubmitted?.();
      onClose();
      
      // Reset form
      setRating(5);
      if (commentRef.current) {
        commentRef.current.value = '';
      }
    } catch (error) {
      console.error('Error submitting review:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkip = () => {
    onClose();
  };

  // Star Rating Component
  const StarRating = ({ 
    rating, 
    onStarClick 
  }: { 
    rating: number; 
    onStarClick: (rating: number) => void;
  }) => {
    return (
      <div className="flex justify-center gap-1">
        {[1, 2, 3, 4, 5].map(star => (
          <button
            key={star}
            type="button"
            onClick={() => onStarClick(star)}
            className="text-3xl cursor-pointer focus:outline-none hover:scale-110 transition-transform"
          >
            <i 
              className={`fa-${star <= rating ? 'solid' : 'regular'} fa-star ${
                star <= rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  if (!isClient) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-bold">
            {t("reviews.rateYourOrder")}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* Order Summary */}
          {order && (
            <div className="text-center">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                {t("reviews.orderNumber")}: {order.id.slice(-8).toUpperCase()}
              </p>
              <p className="font-medium dark:text-gray-100">
                {order.items.length > 0 
                  ? `${order.items[0].name}${order.items.length > 1 ? ` + ${order.items.length - 1} ${t("common.more")}` : ''}`
                  : t("reviews.yourOrder")}
              </p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Star Rating */}
            <div className="text-center">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                {t("reviews.howWasYourExperience")}
              </label>
              <StarRating rating={rating} onStarClick={setRating} />
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                {rating}/5 {t("reviews.stars")}
              </p>
            </div>

            {/* Comment */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t("reviews.additionalComments")} ({t("common.optional")})
              </label>
              <textarea
                ref={commentRef}
                placeholder={t("reviews.commentPlaceholder")}
                rows={3}
                className="w-full px-3 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100 resize-none"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleSkip}
                className="flex-1"
                disabled={isSubmitting}
              >
                {t("reviews.skipReview")}
              </Button>
              <Button
                type="submit"
                className="flex-1 bg-[#56999B] dark:bg-[#5DBDC0] hover:bg-[#56999B]/90 dark:hover:bg-[#4A9EA0]"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                    {t("reviews.submitting")}
                  </>
                ) : (
                  t("reviews.submitReview")
                )}
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
